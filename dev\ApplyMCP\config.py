import os
from pathlib import Path

class Config:
    # LLM配置
    LLM_BASE_URL = "https://api.studio.nebius.ai/v1"
    LLM_MODEL = "deepseek-ai/DeepSeek-V3-0324-fast"
    
    # MCP配置
    MCP_SERVER_SCRIPT = "file_operations_mcp.py"
    
    # 文件操作配置
    DEFAULT_WORK_DIR = "./workspace"
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS = ['.txt', '.py', '.json', '.md', '.yaml', '.yml', '.xml', '.csv']
    
    @classmethod
    def get_llm_api_key(cls):
        api_file = Path(__file__).parent.parent / "api.txt"
        with open(api_file, 'r', encoding='utf-8') as f:
            return f.read().strip()
