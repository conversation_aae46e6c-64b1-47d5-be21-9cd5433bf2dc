#!/usr/bin/env python3
"""
文件操作MCP服务器
提供基本的文件读写、创建、删除等操作功能
使用简化的JSON-RPC协议实现
"""

import asyncio
import json
import os
import shutil
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional

# 简化的JSON-RPC服务器类
class SimpleJSONRPCServer:
    def __init__(self):
        self.tools = {
            "read_file": self.read_file,
            "write_file": self.write_file,
            "create_directory": self.create_directory,
            "delete_file": self.delete_file,
            "list_directory": self.list_directory,
            "copy_file": self.copy_file,
            "move_file": self.move_file,
            "get_file_info": self.get_file_info
        }

    async def handle_request(self, request_data):
        """处理JSON-RPC请求"""
        try:
            request = json.loads(request_data)
            method = request.get("method")
            params = request.get("params", {})
            request_id = request.get("id")

            if method == "list_tools":
                return self.create_response(request_id, self.get_tools_list())
            elif method == "call_tool":
                tool_name = params.get("name")
                arguments = params.get("arguments", {})
                result = await self.call_tool(tool_name, arguments)
                return self.create_response(request_id, result)
            else:
                return self.create_error_response(request_id, f"未知方法: {method}")

        except Exception as e:
            return self.create_error_response(None, f"请求处理错误: {str(e)}")

    def create_response(self, request_id, result):
        """创建成功响应"""
        return json.dumps({
            "jsonrpc": "2.0",
            "id": request_id,
            "result": result
        })

    def create_error_response(self, request_id, error_message):
        """创建错误响应"""
        return json.dumps({
            "jsonrpc": "2.0",
            "id": request_id,
            "error": {
                "code": -1,
                "message": error_message
            }
        })

    def get_tools_list(self):
        """获取工具列表"""
        return {
            "tools": [
                {
                    "name": "read_file",
                    "description": "读取文件内容",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "file_path": {"type": "string", "description": "要读取的文件路径"}
                        },
                        "required": ["file_path"]
                    }
                },
                {
                    "name": "write_file",
                    "description": "写入文件内容",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "file_path": {"type": "string", "description": "要写入的文件路径"},
                            "content": {"type": "string", "description": "要写入的内容"},
                            "mode": {"type": "string", "enum": ["write", "append"], "description": "写入模式", "default": "write"}
                        },
                        "required": ["file_path", "content"]
                    }
                },
                {
                    "name": "create_directory",
                    "description": "创建目录",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "dir_path": {"type": "string", "description": "要创建的目录路径"}
                        },
                        "required": ["dir_path"]
                    }
                },
                {
                    "name": "delete_file",
                    "description": "删除文件",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "file_path": {"type": "string", "description": "要删除的文件路径"}
                        },
                        "required": ["file_path"]
                    }
                },
                {
                    "name": "list_directory",
                    "description": "列出目录内容",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "dir_path": {"type": "string", "description": "要列出的目录路径"}
                        },
                        "required": ["dir_path"]
                    }
                },
                {
                    "name": "copy_file",
                    "description": "复制文件",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "source_path": {"type": "string", "description": "源文件路径"},
                            "dest_path": {"type": "string", "description": "目标文件路径"}
                        },
                        "required": ["source_path", "dest_path"]
                    }
                },
                {
                    "name": "move_file",
                    "description": "移动/重命名文件",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "source_path": {"type": "string", "description": "源文件路径"},
                            "dest_path": {"type": "string", "description": "目标文件路径"}
                        },
                        "required": ["source_path", "dest_path"]
                    }
                },
                {
                    "name": "get_file_info",
                    "description": "获取文件信息",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "file_path": {"type": "string", "description": "文件路径"}
                        },
                        "required": ["file_path"]
                    }
                }
            ]
        }

    async def call_tool(self, tool_name, arguments):
        """调用工具"""
        if tool_name not in self.tools:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"未知工具: {tool_name}"}]
            }

        try:
            result = await self.tools[tool_name](arguments)
            return result
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"工具执行错误: {str(e)}"}]
            }

    async def read_file(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """读取文件内容"""
        file_path = args["file_path"]
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return {
                "success": True,
                "content": [{"type": "text", "text": f"文件内容:\n{content}"}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"读取文件失败: {str(e)}"}]
            }

    async def write_file(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """写入文件内容"""
        file_path = args["file_path"]
        content = args["content"]
        mode = args.get("mode", "write")

        try:
            # 确保目录存在
            dir_path = os.path.dirname(file_path)
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)

            file_mode = 'w' if mode == "write" else 'a'
            with open(file_path, file_mode, encoding='utf-8') as f:
                f.write(content)

            action = "写入" if mode == "write" else "追加"
            return {
                "success": True,
                "content": [{"type": "text", "text": f"成功{action}文件: {file_path}"}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"写入文件失败: {str(e)}"}]
            }

    async def create_directory(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """创建目录"""
        dir_path = args["dir_path"]
        try:
            os.makedirs(dir_path, exist_ok=True)
            return {
                "success": True,
                "content": [{"type": "text", "text": f"成功创建目录: {dir_path}"}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"创建目录失败: {str(e)}"}]
            }

    async def delete_file(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """删除文件"""
        file_path = args["file_path"]
        try:
            if os.path.isfile(file_path):
                os.remove(file_path)
                return {
                    "success": True,
                    "content": [{"type": "text", "text": f"成功删除文件: {file_path}"}]
                }
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)
                return {
                    "success": True,
                    "content": [{"type": "text", "text": f"成功删除目录: {file_path}"}]
                }
            else:
                return {
                    "success": False,
                    "content": [{"type": "text", "text": f"文件或目录不存在: {file_path}"}]
                }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"删除失败: {str(e)}"}]
            }

    async def list_directory(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """列出目录内容"""
        dir_path = args["dir_path"]
        try:
            if not os.path.isdir(dir_path):
                return {
                    "success": False,
                    "content": [{"type": "text", "text": f"目录不存在: {dir_path}"}]
                }

            items = []
            for item in os.listdir(dir_path):
                item_path = os.path.join(dir_path, item)
                item_type = "目录" if os.path.isdir(item_path) else "文件"
                items.append(f"{item_type}: {item}")

            content = f"目录 {dir_path} 的内容:\n" + "\n".join(items)
            return {
                "success": True,
                "content": [{"type": "text", "text": content}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"列出目录失败: {str(e)}"}]
            }

    async def copy_file(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """复制文件"""
        source_path = args["source_path"]
        dest_path = args["dest_path"]
        try:
            # 确保目标目录存在
            dir_path = os.path.dirname(dest_path)
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)
            shutil.copy2(source_path, dest_path)
            return {
                "success": True,
                "content": [{"type": "text", "text": f"成功复制文件: {source_path} -> {dest_path}"}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"复制文件失败: {str(e)}"}]
            }

    async def move_file(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """移动/重命名文件"""
        source_path = args["source_path"]
        dest_path = args["dest_path"]
        try:
            # 确保目标目录存在
            dir_path = os.path.dirname(dest_path)
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)
            shutil.move(source_path, dest_path)
            return {
                "success": True,
                "content": [{"type": "text", "text": f"成功移动文件: {source_path} -> {dest_path}"}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"移动文件失败: {str(e)}"}]
            }

    async def get_file_info(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """获取文件信息"""
        file_path = args["file_path"]
        try:
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "content": [{"type": "text", "text": f"文件或目录不存在: {file_path}"}]
                }

            stat = os.stat(file_path)
            info = {
                "路径": file_path,
                "类型": "目录" if os.path.isdir(file_path) else "文件",
                "大小": f"{stat.st_size} 字节",
                "修改时间": str(stat.st_mtime),
                "创建时间": str(stat.st_ctime)
            }

            content = "文件信息:\n" + "\n".join([f"{k}: {v}" for k, v in info.items()])
            return {
                "success": True,
                "content": [{"type": "text", "text": content}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"获取文件信息失败: {str(e)}"}]
            }

async def main():
    """启动简化的JSON-RPC服务器"""
    server = SimpleJSONRPCServer()

    try:
        while True:
            # 从stdin读取请求
            line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
            if not line:
                break

            line = line.strip()
            if not line:
                continue

            # 处理请求
            response = await server.handle_request(line)

            # 发送响应到stdout
            print(response, flush=True)

    except KeyboardInterrupt:
        pass
    except Exception as e:
        error_response = server.create_error_response(None, f"服务器错误: {str(e)}")
        print(error_response, flush=True)

if __name__ == "__main__":
    asyncio.run(main())
